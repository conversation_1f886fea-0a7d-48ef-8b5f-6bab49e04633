"""
Test script to verify the new Bedrock session management integration
"""

import asyncio
import logging
from session_manager import session_manager

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_session_integration():
    """Test the new session manager integration"""
    
    print("🧪 Testing New Bedrock Session Manager Integration")
    print("=" * 60)
    
    try:
        # Test 1: Create a session
        print("\n1️⃣ Testing session creation...")
        session_id = "test_session_123"
        chat_session = session_manager.get_or_create_session(session_id)
        print(f"✅ Session created: {chat_session.session_id}")
        
        # Test 2: Add conversation turns
        print("\n2️⃣ Testing conversation turns...")
        chat_session.add_turn(
            user_message="What are my AWS costs?",
            assistant_response="I'll help you analyze your AWS costs. Let me check your billing information.",
            tools_used=[
                {"tool_name": "get_cost_and_usage", "server_name": "cost-explorer", "success": True}
            ]
        )
        print(f"✅ Added turn 1. Total turns: {len(chat_session.conversation_history)}")
        
        chat_session.add_turn(
            user_message="Can you break that down by service?",
            assistant_response="Here's your cost breakdown by AWS service for this month.",
            tools_used=[
                {"tool_name": "get_dimension_values", "server_name": "cost-explorer", "success": True}
            ]
        )
        print(f"✅ Added turn 2. Total turns: {len(chat_session.conversation_history)}")
        
        # Test 3: Get session stats
        print("\n3️⃣ Testing session statistics...")
        stats = chat_session.get_session_stats()
        print(f"✅ Session stats retrieved:")
        print(f"   - Total turns: {stats['total_turns']}")
        print(f"   - Total tools used: {stats['total_tools_used']}")
        print(f"   - Successful tools: {stats['successful_tools']}")
        print(f"   - Bedrock status: {stats['bedrock_session_status']}")
        
        # Test 4: Get Bedrock messages format
        print("\n4️⃣ Testing Bedrock message format...")
        messages = chat_session.get_bedrock_messages(max_turns=5)
        print(f"✅ Generated {len(messages)} Bedrock messages")
        for i, msg in enumerate(messages[:2]):  # Show first 2
            print(f"   Message {i+1}: {msg['role']} - {msg['content'][0]['text'][:50]}...")
        
        # Test 5: Get context for Bedrock
        print("\n5️⃣ Testing context generation...")
        context = chat_session.get_context_for_bedrock()
        print(f"✅ Generated context summary:")
        print(f"   {context[:100]}...")
        
        # Test 6: Session manager global stats
        print("\n6️⃣ Testing global session stats...")
        global_stats = session_manager.get_all_sessions_stats()
        print(f"✅ Global stats:")
        print(f"   - Total sessions: {global_stats['total_sessions']}")
        print(f"   - Active sessions: {global_stats['active_sessions']}")
        print(f"   - Total turns: {global_stats['total_conversation_turns']}")
        print(f"   - Total tools: {global_stats['total_tools_executed']}")
        
        # Test 7: Session cleanup
        print("\n7️⃣ Testing session cleanup...")
        cleaned = session_manager.cleanup_expired_sessions()
        print(f"✅ Cleaned up {cleaned} expired sessions")
        
        print("\n🎉 All tests passed! Integration is working correctly.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_session_manager_import():
    """Test basic session manager import and initialization"""
    try:
        print("📦 Testing session manager import...")
        from session_manager import SessionManager, SessionConfig, BedrockSessionClient
        print("✅ All classes imported successfully")
        
        print("🔧 Testing configuration...")
        config = SessionConfig(
            region_name="us-east-1",
            session_timeout_hours=2,
            max_conversation_turns=100
        )
        config.validate()
        print("✅ Configuration validated")
        
        print("🔗 Testing Bedrock client...")
        # Note: This will fail if AWS credentials are not configured
        # but it tests the class instantiation
        try:
            client = BedrockSessionClient(region_name="us-east-1")
            print("✅ Bedrock client created (credentials may not be configured)")
        except Exception as e:
            print(f"⚠️ Bedrock client creation failed (expected if no AWS creds): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Integration Tests")
    print("=" * 60)
    
    # Test 1: Basic imports
    import_success = test_session_manager_import()
    
    if import_success:
        # Test 2: Full integration (async)
        try:
            asyncio.run(test_session_integration())
        except Exception as e:
            print(f"❌ Async test failed: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🏁 Integration tests completed")
