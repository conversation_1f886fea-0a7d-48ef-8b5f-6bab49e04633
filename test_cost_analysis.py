#!/usr/bin/env python3
"""
Direct Cost Analysis Test Script
Tests the MCP cost analysis tools directly WITHOUT optimizer
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Import our MCP components but bypass optimizer
from main import MCPServerConfig, MCPServerConnection, get_executable_name

load_dotenv()

# Define servers without importing the full manager
DEFAULT_MCP_SERVERS = [
    MCPServerConfig(
        name="cost-explorer",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-cost-explorer-mcp-server@latest",
            get_executable_name("awslabs.cost-explorer-mcp-server")
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        description="AWS Cost Explorer MCP Server for cost analysis and billing insights",
        enabled=True
    )
]

async def call_tool_direct(connection, tool_name, arguments):
    """Call tool directly without optimizer"""
    try:
        print(f"📞 Calling {tool_name} with arguments:")
        print(json.dumps(arguments, indent=2))

        result = await connection.session.call_tool(tool_name, arguments)
        return {"success": True, "result": result}
    except Exception as e:
        return {"success": False, "error": str(e)}

async def test_cost_analysis():
    """Test cost analysis functionality directly"""

    print("🔍 Testing AWS Cost Analysis Tools (NO OPTIMIZER)")
    print("=" * 50)

    # Create direct connection to cost-explorer
    cost_explorer_config = DEFAULT_MCP_SERVERS[0]
    connection = MCPServerConnection(cost_explorer_config)
    
    try:
        # Connect to cost-explorer directly
        print("📡 Connecting to cost-explorer...")
        success = await connection.connect()
        if success:
            print("✅ cost-explorer connected successfully")
            print(f"📋 Available tools: {list(connection.tools.keys())}")
        else:
            print(f"❌ Failed to connect to cost-explorer: {connection.error}")
            return
        
        # Test 1: Get cost and usage for last 10 days
        print("\n📊 Test 1: Bedrock costs for last 10 days")
        print("-" * 40)
        
        # Calculate date range (last 10 days)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=10)
        
        cost_params = {
            "date_range": {
                "start": start_date.strftime("%Y-%m-%d"),
                "end": end_date.strftime("%Y-%m-%d")
            },
            "granularity": "DAILY",
            "group_by": [{"type": "DIMENSION", "key": "SERVICE"}],
            "filter_expression": {
                "dimensions": {
                    "key": "SERVICE",
                    "values": ["Amazon Bedrock"],
                    "match_options": ["EQUALS"]
                }
            }
        }
        
        if "get_cost_and_usage" in connection.tools:
            print(f"📈 Getting Bedrock costs from {start_date} to {end_date}")
            result = await call_tool_direct(connection, "get_cost_and_usage", cost_params)
            
            if result["success"]:
                print("✅ Bedrock cost analysis successful!")
                cost_data = result["result"]
                print(json.dumps(cost_data, indent=2))
            else:
                print(f"❌ Bedrock cost analysis failed: {result.get('error', 'Unknown error')}")
        
        # Test 2: Get overall account costs for ap-south-1
        print("\n🌏 Test 2: Account costs in ap-south-1 region")
        print("-" * 40)
        
        region_params = {
            "date_range": {
                "start": start_date.strftime("%Y-%m-%d"),
                "end": end_date.strftime("%Y-%m-%d")
            },
            "granularity": "DAILY",
            "group_by": [{"type": "DIMENSION", "key": "SERVICE"}],
            "filter_expression": {
                "dimensions": {
                    "key": "REGION",
                    "values": ["ap-south-1"],
                    "match_options": ["EQUALS"]
                }
            }
        }
        
        if "get_cost_and_usage" in connection.tools:
            print(f"📊 Getting ap-south-1 costs from {start_date} to {end_date}")
            result = await call_tool_direct(connection, "get_cost_and_usage", region_params)
            
            if result["success"]:
                print("✅ Regional cost analysis successful!")
                cost_data = result["result"]
                print(json.dumps(cost_data, indent=2))
            else:
                print(f"❌ Regional cost analysis failed: {result.get('error', 'Unknown error')}")
        
        # Test 3: Get cost forecast
        print("\n🔮 Test 3: Cost forecast")
        print("-" * 40)
        
        forecast_params = {
            "date_range": {
                "start": end_date.strftime("%Y-%m-%d"),
                "end": (end_date + timedelta(days=30)).strftime("%Y-%m-%d")
            },
            "granularity": "MONTHLY",
            "metric": "BLENDED_COST"
        }
        
        if "get_cost_forecast" in connection.tools:
            print("🔮 Getting 30-day cost forecast")
            result = await call_tool_direct(connection, "get_cost_forecast", forecast_params)
            
            if result["success"]:
                print("✅ Cost forecast successful!")
                forecast_data = result["result"]
                print(json.dumps(forecast_data, indent=2))
            else:
                print(f"❌ Cost forecast failed: {result.get('error', 'Unknown error')}")
        
    except Exception as e:
        print(f"❌ Error during cost analysis: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        if connection.session:
            await connection.disconnect()
        print("✅ Cleanup complete")

if __name__ == "__main__":
    asyncio.run(test_cost_analysis())
