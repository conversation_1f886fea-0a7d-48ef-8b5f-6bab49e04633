"""
Session Management for MCP Bot with Context Retention
Handles conversation history, context tracking, and session persistence
Integrated with Amazon Bedrock Session Management APIs (Preview)

Key improvements:
- Correct PutInvocationStep payload (no double JSON; proper contentBlocks) 
- Session recovery from Bedrock on restart (list + get step payloads)
- Optimized context summary computation with caching
- Optional async client (aioboto3) and circuit breaker retries (tenacity)
- Input validation and configuration
- Health checks and observability helpers
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass
from uuid import uuid4
import json
import logging
import re

# AWS imports
import boto3
from botocore.exceptions import ClientError

# Optional deps
try:
    import aioboto3  # type: ignore
except Exception:
    aioboto3 = None

try:
    from tenacity import retry, stop_after_attempt, wait_exponential  # type: ignore
except Exception:
    retry = None

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


# -------------------------
# Configuration
# -------------------------

@dataclass
class SessionConfig:
    region_name: Optional[str] = None
    encryption_key_arn: Optional[str] = None
    session_timeout_hours: int = 2
    max_conversation_turns: int = 200
    max_tools_per_turn: int = 25
    enable_cleanup: bool = True
    delete_on_cleanup: bool = False
    # Reconstruction and compression
    recovery_max_steps: int = 200
    compress_keep_recent: int = 20
    # Limits to avoid oversized payloads
    max_user_message_len: int = 20000
    max_assistant_response_len: int = 20000
    max_metadata_len: int = 5000
    # Whether to persist minimal tool info in steps
    persist_tools_minimal: bool = True

    def validate(self) -> None:
        if self.session_timeout_hours <= 0:
            raise ValueError("session_timeout_hours must be positive")
        if self.max_conversation_turns <= 0:
            raise ValueError("max_conversation_turns must be positive")
        if self.max_tools_per_turn < 0:
            raise ValueError("max_tools_per_turn must be non-negative")


# -------------------------
# Data Model
# -------------------------

@dataclass
class ConversationTurn:
    """Represents a single conversation turn with context"""
    timestamp: datetime
    user_message: str
    assistant_response: str
    tools_used: List[Dict[str, Any]]
    session_id: str

    def to_dict(self) -> Dict[str, Any]:
        return {
            "timestamp": self.timestamp.isoformat(),
            "user_message": self.user_message,
            "assistant_response": self.assistant_response,
            "tools_used": self.tools_used,
            "session_id": self.session_id
        }


# -------------------------
# Bedrock Session Backend
# -------------------------

def _with_retry(fn):
    # Attach tenacity retry if available; otherwise return original
    if retry is None:
        return fn
    return retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=6))(fn)


class BedrockSessionClient:
    """
    Wrapper over Agents for Amazon Bedrock Runtime session APIs:
      - create_session, get_session, end_session, delete_session
      - create_invocation, list_invocations
      - put_invocation_step, list_invocation_steps, get_invocation_step
    """

    def __init__(
        self,
        region_name: Optional[str] = None,
        encryption_key_arn: Optional[str] = None,
        session_metadata: Optional[Dict[str, str]] = None,
    ):
        self.client = boto3.client("bedrock-agent-runtime", region_name=region_name)
        self.encryption_key_arn = encryption_key_arn
        self.default_metadata = session_metadata or {}

    # -------- Session --------

    @_with_retry
    def create_session(self, tags: Optional[Dict[str, str]] = None) -> str:
        params: Dict[str, Any] = {}
        if self.encryption_key_arn:
            params["encryptionKeyArn"] = self.encryption_key_arn
        if self.default_metadata:
            params["sessionMetadata"] = self.default_metadata
        if tags:
            params["tags"] = tags
        resp = self.client.create_session(**params)
        return resp["sessionId"]

    @_with_retry
    def get_session(self, session_identifier: str) -> Dict[str, Any]:
        return self.client.get_session(sessionIdentifier=session_identifier)

    @_with_retry
    def end_session(self, session_identifier: str) -> None:
        self.client.end_session(sessionIdentifier=session_identifier)

    @_with_retry
    def delete_session(self, session_identifier: str) -> None:
        self.client.delete_session(sessionIdentifier=session_identifier)

    # -------- Invocation grouping --------

    @_with_retry
    def create_invocation(self, session_identifier: str, description: Optional[str] = None) -> str:
        params: Dict[str, Any] = {"sessionIdentifier": session_identifier}
        if description:
            params["description"] = description
        resp = self.client.create_invocation(**params)
        return resp["invocationId"]

    @_with_retry
    def list_invocations(self, session_identifier: str, max_results: int = 20) -> List[Dict[str, Any]]:
        resp = self.client.list_invocations(sessionIdentifier=session_identifier, maxResults=max_results)
        return resp.get("invocationSummaries", [])

    # -------- Steps (fine-grained checkpoints) --------

    def _minimize_tools(self, tools_used: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        minimal = []
        for t in tools_used:
            minimal.append({
                "tool_name": t.get("tool_name"),
                "server_name": t.get("server_name"),
                "success": t.get("success"),
            })
        return minimal

    @_with_retry
    def put_invocation_step_structured(
        self,
        session_identifier: str,
        invocation_identifier: str,
        role: str,
        text: str,
        tools_used: Optional[List[Dict[str, Any]]] = None,
        timestamp: Optional[datetime] = None,
        invocation_step_id: Optional[str] = None,
        persist_tools_minimal: bool = True,
        max_metadata_len: int = 5000,
    ) -> str:
        """
        Store structured step data with proper contentBlocks (no double JSON encoding).
        Adds a metadata block as text, and optionally a compact tools block.
        """
        step_id = invocation_step_id or str(uuid4())
        ts = timestamp or datetime.now(timezone.utc)

        content_blocks = []
        # Main text block
        if text:
            content_blocks.append({"text": text})

        # Metadata block (compact)
        minimal_tools = (self._minimize_tools(tools_used or []) if persist_tools_minimal else [])
        metadata = {
            "role": role,
            "timestamp": ts.isoformat(),
            "tools_summary": {
                "count": len(minimal_tools),
                "successful": sum(1 for t in minimal_tools if t.get("success")),
                "servers": sorted(list({t.get("server_name") for t in minimal_tools if t.get("server_name")})),
            },
        }
        metadata_json = json.dumps(metadata)
        if len(metadata_json) <= max_metadata_len:
            content_blocks.append({"text": f"[METADATA]{metadata_json}"})

        # Optional minimal tools payload as another block
        if minimal_tools:
            tools_json = json.dumps({"tools": minimal_tools})
            if len(tools_json) <= max_metadata_len:
                content_blocks.append({"text": f"[TOOLS]{tools_json}"})

        payload = {"contentBlocks": content_blocks}

        # Persist
        self.client.put_invocation_step(
            sessionIdentifier=session_identifier,
            invocationIdentifier=invocation_identifier,
            invocationStepId=step_id,
            invocationStepTime=ts,
            payload=payload,
        )
        return step_id

    @_with_retry
    def list_invocation_steps(
        self,
        session_identifier: str,
        invocation_identifier: Optional[str] = None,
        max_steps: int = 100,
        next_token: Optional[str] = None,
    ) -> Tuple[List[Dict[str, Any]], Optional[str]]:
        """
        Lists step summaries; for payloads, call get_invocation_step() per step.
        """
        params: Dict[str, Any] = {"sessionIdentifier": session_identifier}
        if invocation_identifier:
            params["invocationIdentifier"] = invocation_identifier
        if next_token:
            params["nextToken"] = next_token
        resp = self.client.list_invocation_steps(**params)
        steps = resp.get("invocationStepSummaries", [])
        return steps, resp.get("nextToken")

    @_with_retry
    def get_invocation_step(
        self,
        session_identifier: str,
        invocation_identifier: str,
        invocation_step_id: str,
    ) -> Dict[str, Any]:
        return self.client.get_invocation_step(
            sessionIdentifier=session_identifier,
            invocationIdentifier=invocation_identifier,
            invocationStepId=invocation_step_id,
        )


# -------------------------
# Optional Async Backend
# -------------------------

class AsyncBedrockSessionClient:
    """
    Optional async client using aioboto3 for non-blocking I/O.
    Only created if aioboto3 is installed.
    """

    def __init__(self, region_name: Optional[str] = None):
        if aioboto3 is None:
            raise RuntimeError("aioboto3 not installed")
        self.region_name = region_name
        self._session = None
        self.client = None

    async def __aenter__(self):
        self._session = aioboto3.Session()
        self.client = await self._session.client(
            "bedrock-agent-runtime", region_name=self.region_name
        ).__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.__aexit__(exc_type, exc_val, exc_tb)

    async def create_session(self, **kwargs) -> str:
        resp = await self.client.create_session(**kwargs)
        return resp["sessionId"]

    # Add other async wrappers as needed:
    # get_session, end_session, create_invocation, put_invocation_step, etc.


# -------------------------
# Chat Session (enhanced)
# -------------------------

class ChatSession:
    """
    Manages individual chat session with conversation history and context,
    persisted via Bedrock sessions and locally cached for quick access.
    """

    def __init__(
        self,
        session_id: Optional[str] = None,
        bedrock_backend: Optional[BedrockSessionClient] = None,
        invocation_description: Optional[str] = "mcp-bot conversation",
        config: Optional[SessionConfig] = None,
    ):
        self.config = config or SessionConfig()
        self.config.validate()

        self.bedrock = bedrock_backend or BedrockSessionClient(region_name=self.config.region_name, encryption_key_arn=self.config.encryption_key_arn)
        # Create or reuse a Bedrock session
        self.session_id = session_id or self.bedrock.create_session()
        self.created_at = datetime.now(timezone.utc)
        self.last_activity = datetime.now(timezone.utc)

        # Local cache for fast access and message formatting
        self.conversation_history: List[ConversationTurn] = []
        self.context_summary = ""
        self.total_tools_used = 0
        self._last_summary_turn_count = 0

        # Current invocation groups a set of steps
        self.current_invocation_id: Optional[str] = self.bedrock.create_invocation(
            session_identifier=self.session_id,
            description=invocation_description
        )

        # Attempt to warm start from Bedrock (optional)
        try:
            self.load_from_bedrock(max_steps=self.config.recovery_max_steps)
        except Exception as e:
            logger.warning(f"Warm recovery skipped for session {self.session_id}: {e}")

    def _ensure_invocation(self):
        if not self.current_invocation_id:
            self.current_invocation_id = self.bedrock.create_invocation(
                session_identifier=self.session_id,
                description="mcp-bot conversation"
            )

    # ------------- Validation -------------

    def _validate_turn(self, user_message: str, assistant_response: str, tools_used: Optional[List[Dict]]):
        if not user_message or len(user_message.strip()) == 0:
            raise ValueError("user_message cannot be empty")
        if not assistant_response or len(assistant_response.strip()) == 0:
            raise ValueError("assistant_response cannot be empty")
        if len(user_message) > self.config.max_user_message_len:
            raise ValueError("user_message too long")
        if len(assistant_response) > self.config.max_assistant_response_len:
            raise ValueError("assistant_response too long")
        tools_used = tools_used or []
        if len(tools_used) > self.config.max_tools_per_turn:
            raise ValueError(f"Too many tools used: {len(tools_used)}")

    # ------------- Add Turn -------------

    def add_turn(self, user_message: str, assistant_response: str, tools_used: Optional[List[Dict]] = None):
        """Add a conversation turn with context tracking; persist to Bedrock steps."""
        self._validate_turn(user_message, assistant_response, tools_used)
        tools_used = tools_used or []
        now = datetime.now(timezone.utc)

        # Local cache append
        turn = ConversationTurn(
            timestamp=now,
            user_message=user_message,
            assistant_response=assistant_response,
            tools_used=tools_used,
            session_id=self.session_id
        )
        self.conversation_history.append(turn)
        if len(self.conversation_history) > self.config.max_conversation_turns:
            self.compress_old_turns(keep_recent=self.config.compress_keep_recent)
        self.last_activity = now
        self.total_tools_used += len(tools_used)

        # Persist to Bedrock as fine-grained steps (user then assistant)
        self._ensure_invocation()
        try:
            self.bedrock.put_invocation_step_structured(
                session_identifier=self.session_id,
                invocation_identifier=self.current_invocation_id,
                role="user",
                text=user_message,
                tools_used=[],
                timestamp=now,
                persist_tools_minimal=False,
                max_metadata_len=self.config.max_metadata_len,
            )
            self.bedrock.put_invocation_step_structured(
                session_identifier=self.session_id,
                invocation_identifier=self.current_invocation_id,
                role="assistant",
                text=assistant_response,
                tools_used=tools_used,
                timestamp=now,
                persist_tools_minimal=self.config.persist_tools_minimal,
                max_metadata_len=self.config.max_metadata_len,
            )
        except ClientError as e:
            logger.warning(f"Bedrock step persistence failed for session {self.session_id}: {e}")

        # Update context summary (optimized)
        self._update_context_summary_optimized()

        logger.info(f"Added turn to session {self.session_id}: {len(tools_used)} tools used")

    # ------------- Messages for model -------------

    def get_bedrock_messages(self, max_turns: int = 10) -> List[Dict]:
        """Convert conversation history to Bedrock message format with context."""
        messages = []
        recent_turns = self.conversation_history[-max_turns:]

        for turn in recent_turns:
            messages.append({"role": "user", "content": [{"text": turn.user_message}]})
            messages.append({"role": "assistant", "content": [{"text": turn.assistant_response}]})

        logger.info(f"Retrieved {len(messages)} messages for session {self.session_id}")
        return messages

    def get_context_for_bedrock(self) -> str:
        """Generate context summary for system message"""
        context_parts = []

        if self.conversation_history:
            context_parts.append(f"Session started: {self.created_at.strftime('%Y-%m-%d %H:%M')}")
            context_parts.append(f"Total conversation turns: {len(self.conversation_history)}")
            context_parts.append(f"Total tools executed: {self.total_tools_used}")

            if self.context_summary:
                context_parts.append(f"Recent context: {self.context_summary}")

            # Add recent tool usage context
            recent_tools = []
            recent_servers = set()

            for turn in self.conversation_history[-3:]:
                for tool in turn.tools_used:
                    if tool.get("success"):
                        tool_name = tool.get('tool_name', 'unknown')
                        server_name = tool.get('server_name', 'unknown')
                        recent_tools.append(tool_name)
                        recent_servers.add(server_name)

            if recent_tools:
                context_parts.append(f"Recently used tools: {', '.join(set(recent_tools))}")
                context_parts.append(f"Active servers: {', '.join(recent_servers)}")

        return "\n".join(context_parts)

    # ------------- Recovery from Bedrock -------------

    def load_from_bedrock(self, max_steps: int = 50) -> None:
        """Recover conversation history from Bedrock session"""
        collected: List[Dict[str, Any]] = []
        next_token: Optional[str] = None

        # Iterate step summaries
        while len(collected) < max_steps:
            batch, next_token = self.bedrock.list_invocation_steps(
                session_identifier=self.session_id,
                max_steps=max_steps - len(collected),
                next_token=next_token
            )
            if not batch:
                break
            collected.extend(batch)
            if not next_token:
                break

        # For each step summary, fetch full payload
        temp_turns: Dict[str, Dict[str, Any]] = {}
        for s in collected:
            inv_id = s.get("invocationIdentifier") or s.get("invocationId")
            step_id = s.get("invocationStepId")
            step_time = s.get("invocationStepTime")

            if not inv_id or not step_id:
                continue
            try:
                detail = self.bedrock.get_invocation_step(
                    session_identifier=self.session_id,
                    invocation_identifier=inv_id,
                    invocation_step_id=step_id,
                )
            except ClientError as e:
                logger.warning(f"Failed to get step payload for {step_id}: {e}")
                continue

            parsed = self._parse_step_data(detail, fallback_time=step_time)
            if not parsed:
                continue

            turn_key = parsed.get('turn_id') or parsed.get('timestamp') or step_time
            if turn_key not in temp_turns:
                temp_turns[turn_key] = {"timestamp": parsed.get('timestamp')}

            role = parsed.get('role')
            if role == "user":
                temp_turns[turn_key]['user'] = parsed
            elif role == "assistant":
                temp_turns[turn_key]['assistant'] = parsed

        # Convert to ConversationTurn objects (ordered by timestamp)
        recovered = 0
        sorted_turns = sorted(
            temp_turns.values(),
            key=lambda x: x.get('timestamp', '')
        )
        for t in sorted_turns:
            if 'user' in t and 'assistant' in t:
                try:
                    ts = datetime.fromisoformat((t.get('timestamp') or '').replace('Z', '+00:00'))
                except Exception:
                    ts = datetime.now(timezone.utc)
                self.conversation_history.append(ConversationTurn(
                    timestamp=ts,
                    user_message=t['user'].get('text', ''),
                    assistant_response=t['assistant'].get('text', ''),
                    tools_used=t['assistant'].get('tools_used', []),
                    session_id=self.session_id
                ))
                recovered += 1

        if recovered:
            self._update_context_summary_optimized()
            logger.info(f"Recovered {recovered} turns from Bedrock")

    def _parse_step_data(self, step_detail: Dict[str, Any], fallback_time: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Parse a single step detail from get_invocation_step to extract text, role, timestamp, and minimal tools.
        Expects payload.contentBlocks with text items; metadata in a [METADATA]{json} block and tools in [TOOLS]{json}.
        """
        payload = step_detail.get("payload") or {}
        blocks: List[Dict[str, Any]] = payload.get("contentBlocks") or []
        if not blocks:
            return None

        text_content = None
        role = None
        ts = None
        tools_minimal: List[Dict[str, Any]] = []

        for b in blocks:
            if "text" in b:
                txt: str = b["text"]
                if txt.startswith("[METADATA]"):
                    try:
                        meta = json.loads(txt[len("[METADATA]"):])
                        role = role or meta.get("role")
                        ts = ts or meta.get("timestamp")
                    except Exception:
                        pass
                elif txt.startswith("[TOOLS]"):
                    try:
                        tdata = json.loads(txt[len("[TOOLS]"):])
                        tlist = tdata.get("tools") or []
                        # validate shape
                        if isinstance(tlist, list):
                            tools_minimal = [dict(tool_name=t.get("tool_name"), server_name=t.get("server_name"), success=t.get("success")) for t in tlist]
                    except Exception:
                        pass
                else:
                    # first non-prefixed text is the main content
                    if text_content is None:
                        text_content = txt

        if not ts and fallback_time:
            ts = fallback_time if isinstance(fallback_time, str) else None

        return {
            "role": role,
            "text": text_content or "",
            "timestamp": ts,
            "tools_used": tools_minimal,
        }

    # ------------- Context summary (optimized) -------------

    def _extract_topics_fast(self, text: str) -> List[str]:
        topics = []
        low = text.lower()
        if any(k in low for k in ("budget", "cost", "billing")):
            topics.append("cost_analysis")
        if any(k in low for k in ("cloudformation", "stack", "template")):
            topics.append("infrastructure")
        if any(k in low for k in ("pricing", "price", "compare")):
            topics.append("pricing_analysis")
        return topics

    def _categorize_tool(self, server_name: str) -> str:
        s = (server_name or "").lower()
        if "cost-explorer" in s:
            return "cost_analysis"
        if "billing" in s:
            return "billing_management"
        if "cloudformation" in s:
            return "infrastructure_management"
        if "pricing" in s:
            return "pricing_analysis"
        return "other"

    def _update_context_summary_optimized(self):
        """Optimized context analysis with caching"""
        if len(self.conversation_history) < 2:
            return

        if self._last_summary_turn_count == len(self.conversation_history):
            return

        recent_turns = self.conversation_history[-3:]
        topics = set()
        tool_types = set()

        for turn in recent_turns:
            topics.update(self._extract_topics_fast(turn.user_message))
            for tool in turn.tools_used:
                tool_types.add(self._categorize_tool(tool.get('server_name', '')))

        self.context_summary = f"Topics: {','.join(sorted(topics))}; Tools: {','.join(sorted(tool_types))}"
        self._last_summary_turn_count = len(self.conversation_history)

    # ------------- Compression -------------

    def _create_compressed_summary(self, turns: List[ConversationTurn]) -> str:
        # Basic heuristic summarization by topics and last messages
        topic_counts = {"cost_analysis": 0, "infrastructure": 0, "pricing_analysis": 0}
        for t in turns:
            for tp in self._extract_topics_fast(t.user_message):
                if tp in topic_counts:
                    topic_counts[tp] += 1
        last_user = turns[-1].user_message[:120].replace("\n", " ")
        last_assistant = turns[-1].assistant_response[:120].replace("\n", " ")
        return f"topics={topic_counts}, last_user='{last_user}...', last_assistant='{last_assistant}...'"

    def compress_old_turns(self, keep_recent: int = 10):
        """Compress old conversation turns to save memory"""
        if len(self.conversation_history) <= keep_recent:
            return

        old_turns = self.conversation_history[:-keep_recent]
        compressed_summary = self._create_compressed_summary(old_turns)

        # Keep only recent turns + compressed summary
        self.conversation_history = self.conversation_history[-keep_recent:]
        self.context_summary = f"Previous context: {compressed_summary}; {self.context_summary}"

    # ------------- Stats -------------

    def get_session_stats(self) -> Dict[str, Any]:
        successful_tools = 0
        failed_tools = 0
        servers_used = set()

        for turn in self.conversation_history:
            for tool in turn.tools_used:
                if tool.get("success"):
                    successful_tools += 1
                else:
                    failed_tools += 1

                server_name = tool.get("server_name")
                if server_name:
                    servers_used.add(server_name)

        br_status = "UNKNOWN"
        try:
            sess = self.bedrock.get_session(self.session_id)
            br_status = sess.get("sessionStatus", "UNKNOWN")
        except ClientError as e:
            logger.warning(f"Failed to get Bedrock session status for {self.session_id}: {e}")

        return {
            "session_id": self.session_id,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "total_turns": len(self.conversation_history),
            "total_tools_used": self.total_tools_used,
            "successful_tools": successful_tools,
            "failed_tools": failed_tools,
            "servers_used": list(servers_used),
            "context_summary": self.context_summary,
            "bedrock_session_status": br_status,
        }


# -------------------------
# Session Manager (enhanced)
# -------------------------

class SessionManager:
    """Manages multiple chat sessions with automatic cleanup and Bedrock lifecycle"""

    def __init__(
        self,
        config: Optional[SessionConfig] = None,
        default_session_metadata: Optional[Dict[str, str]] = None,
    ):
        self.config = config or SessionConfig()
        self.config.validate()

        self.sessions: Dict[str, ChatSession] = {}
        self.session_timeout = timedelta(hours=self.config.session_timeout_hours)

        self.backend = BedrockSessionClient(
            region_name=self.config.region_name,
            encryption_key_arn=self.config.encryption_key_arn,
            session_metadata=default_session_metadata,
        )
        logger.info(f"SessionManager initialized with {self.config.session_timeout_hours}h timeout")

    def get_or_create_session(self, session_id: Optional[str] = None) -> ChatSession:
        if session_id and session_id in self.sessions:
            logger.info(f"Retrieved existing session: {session_id}")
            session = self.sessions[session_id]
            session.last_activity = datetime.now(timezone.utc)
            return session

        if not session_id:
            session_id = self.backend.create_session()

        session = ChatSession(
            session_id=session_id,
            bedrock_backend=self.backend,
            config=self.config,
        )
        self.sessions[session_id] = session
        logger.info(f"Created new session: {session_id}")
        return session

    def get_session(self, session_id: str) -> Optional[ChatSession]:
        return self.sessions.get(session_id)

    def delete_session(self, session_id: str) -> bool:
        if session_id in self.sessions:
            try:
                self.backend.end_session(session_id)
                if self.config.delete_on_cleanup:
                    self.backend.delete_session(session_id)
            except ClientError as e:
                logger.warning(f"Failed to end/delete Bedrock session {session_id}: {e}")
            del self.sessions[session_id]
            logger.info(f"Deleted session: {session_id}")
            return True
        return False

    def cleanup_expired_sessions(self) -> int:
        now = datetime.now(timezone.utc)
        expired_sessions = []

        for session_id, session in list(self.sessions.items()):
            if now - session.last_activity > self.session_timeout:
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            try:
                self.backend.end_session(session_id)
                if self.config.delete_on_cleanup:
                    self.backend.delete_session(session_id)
            except ClientError as e:
                logger.warning(f"Failed to end/delete Bedrock session {session_id}: {e}")
            del self.sessions[session_id]

        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")

        return len(expired_sessions)

    def get_all_sessions_stats(self) -> Dict[str, Any]:
        total_sessions = len(self.sessions)
        total_turns = sum(len(session.conversation_history) for session in self.sessions.values())
        total_tools = sum(session.total_tools_used for session in self.sessions.values())

        active_sessions = 0
        now = datetime.now(timezone.utc)
        for session in self.sessions.values():
            if now - session.last_activity < timedelta(minutes=30):
                active_sessions += 1

        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "total_conversation_turns": total_turns,
            "total_tools_executed": total_tools,
            "session_timeout_hours": self.session_timeout.total_seconds() / 3600
        }


# -------------------------
# Health and Observability
# -------------------------

class SessionHealthCheck:
    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager

    def check_bedrock_connectivity(self) -> bool:
        try:
            test_session_id = self.session_manager.backend.create_session()
            self.session_manager.backend.delete_session(test_session_id)
            return True
        except Exception:
            return False

    def get_health_metrics(self) -> Dict[str, Any]:
        stats = self.session_manager.get_all_sessions_stats()
        # Simple memory usage estimator placeholder
        mem_mb = 0.0
        try:
            import psutil  # optional
            process = psutil.Process()
            mem_mb = process.memory_info().rss / (1024 * 1024)
        except Exception:
            pass
        return {
            **stats,
            "memory_usage_mb": round(mem_mb, 2),
            "bedrock_connectivity": self.check_bedrock_connectivity(),
            "health_status": "healthy" if stats["total_sessions"] < 1000 else "warning"
        }


# Global session manager instance (configure as needed)
session_manager = SessionManager(
    config=SessionConfig(
        region_name=None,                # e.g., "us-east-1"
        encryption_key_arn=None,         # e.g., "arn:aws:kms:us-east-1:123456789012:key/...."
        session_timeout_hours=2,
        max_conversation_turns=200,
        max_tools_per_turn=25,
        enable_cleanup=True,
        delete_on_cleanup=False,
        recovery_max_steps=200,
        compress_keep_recent=20,
        max_user_message_len=20000,
        max_assistant_response_len=20000,
        max_metadata_len=5000,
        persist_tools_minimal=True,
    ),
    default_session_metadata={"app": "mcp-bot", "env": "prod"},
)
