"""
Enhanced Session Management for MCP Bot using Amazon Bedrock APIs
Replaces custom session management with native Bedrock capabilities
"""

import boto3
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass
import os

logger = logging.getLogger(__name__)

class BedrockSessionManager:
    """Enhanced session manager using Amazon Bedrock Session Management APIs"""

    def __init__(self, region_name: str = None):
        self.region_name = region_name or os.getenv('AWS_REGION', 'us-east-1')
        self.bedrock_agent_runtime = boto3.client('bedrock-agent-runtime', region_name=self.region_name)
        self.bedrock_runtime = boto3.client('bedrock-runtime', region_name=self.region_name)
        self.active_sessions = {}  # Cache for session metadata
        logger.info(f"BedrockSessionManager initialized in region {self.region_name}")

    def create_session(self, session_id: str, description: str = None) -> Dict[str, Any]:
        """Create a new Bedrock session for conversation management"""
        try:
            # Use correct Bedrock Agent Runtime API parameters
            session_params = {}

            # Add optional parameters if available
            if description:
                session_params['sessionMetadata'] = {
                    'description': description or f"MCP Bot session {session_id}",
                    'session_id': session_id
                }

            response = self.bedrock_agent_runtime.create_session(**session_params)

            session_info = {
                'session_id': response['sessionId'],
                'session_arn': response['sessionArn'],
                'created_at': datetime.now().isoformat()
            }

            self.active_sessions[session_id] = session_info
            logger.info(f"Created Bedrock session: {response['sessionId']}")
            return session_info
        except Exception as e:
            logger.error(f"Failed to create session {session_id}: {str(e)}")
            raise

    def create_invocation(self, session_id: str, invocation_id: str) -> Dict[str, Any]:
        """Create an invocation group within a session"""
        try:
            response = self.bedrock_agent_runtime.create_invocation(
                sessionId=session_id,
                clientRequestToken=invocation_id
            )

            logger.info(f"Created invocation {invocation_id} in session {session_id}")
            return response
        except Exception as e:
            logger.error(f"Failed to create invocation: {str(e)}")
            raise

    def store_conversation_turn(self, session_id: str, invocation_id: str,
                              step_name: str, user_message: str,
                              assistant_response: str, tools_used: List[Dict] = None):
        """Store a conversation turn with context in Bedrock session"""

        step_data = {
            'timestamp': datetime.now().isoformat(),
            'user_message': user_message,
            'assistant_response': assistant_response,
            'tools_used': tools_used or [],
            'metadata': {
                'total_tools': len(tools_used or []),
                'successful_tools': sum(1 for tool in (tools_used or []) if tool.get('success')),
                'servers_used': list(set(tool.get('server_name') for tool in (tools_used or []) if tool.get('server_name')))
            }
        }

        try:
            response = self.bedrock_agent_runtime.put_invocation_step(
                sessionId=session_id,
                invocationId=invocation_id,
                stepName=step_name,
                stepData=step_data
            )

            logger.info(f"Stored conversation turn {step_name} in session {session_id}")
            return response
        except Exception as e:
            logger.error(f"Failed to store conversation turn: {str(e)}")
            raise
    
    def get_conversation_history(self, session_id: str, max_invocations: int = 10) -> List[Dict]:
        """Retrieve conversation history from Bedrock session"""
        try:
            # List recent invocations
            invocations_response = self.bedrock_agent_runtime.list_invocations(
                sessionId=session_id,
                maxItems=max_invocations
            )

            conversation_history = []

            for invocation in invocations_response.get('invocations', []):
                invocation_id = invocation['invocationId']

                # Get steps for this invocation
                try:
                    step_response = self.bedrock_agent_runtime.get_invocation_step(
                        sessionId=session_id,
                        invocationId=invocation_id,
                        stepName='conversation_turn'  # Your step naming convention
                    )

                    step_data = step_response.get('stepData', {})
                    if step_data:
                        conversation_history.append(step_data)

                except Exception as step_error:
                    logger.warning(f"Could not retrieve step data: {step_error}")
                    continue

            return sorted(conversation_history, key=lambda x: x.get('timestamp', ''))

        except Exception as e:
            logger.error(f"Failed to retrieve conversation history: {str(e)}")
            return []
    
    def get_bedrock_messages_for_converse(self, session_id: str, max_turns: int = 10) -> List[Dict]:
        """Convert Bedrock session history to Converse API format"""
        history = self.get_conversation_history(session_id, max_turns)
        messages = []

        for turn in history[-max_turns:]:
            # Add user message
            messages.append({
                "role": "user",
                "content": [{"text": turn['user_message']}]
            })

            # Add assistant response
            messages.append({
                "role": "assistant",
                "content": [{"text": turn['assistant_response']}]
            })

        return messages
    
    def generate_context_for_system_message(self, session_id: str) -> str:
        """Generate rich context summary from session history"""
        try:
            session_info = self.bedrock_agent_runtime.get_session(sessionId=session_id)
            history = self.get_conversation_history(session_id)

            if not history:
                return f"New session {session_id} - no previous context."

            # Analyze session patterns
            total_turns = len(history)
            total_tools = sum(turn.get('metadata', {}).get('total_tools', 0) for turn in history)
            all_servers = set()
            recent_topics = []

            for turn in history[-3:]:  # Analyze recent turns
                metadata = turn.get('metadata', {})
                servers = metadata.get('servers_used', [])
                all_servers.update(servers)

                # Topic detection from user messages
                user_msg = turn.get('user_message', '').lower()
                if 'cost' in user_msg or 'billing' in user_msg:
                    recent_topics.append('cost_analysis')
                if 'cloudformation' in user_msg or 'stack' in user_msg:
                    recent_topics.append('infrastructure')
                if 'pricing' in user_msg:
                    recent_topics.append('pricing_analysis')

            context_parts = [
                f"Session: {session_id}",
                f"Created: {session_info.get('createdTime', 'Unknown')}",
                f"Total conversation turns: {total_turns}",
                f"Total tools executed: {total_tools}",
                f"Active MCP servers: {', '.join(all_servers) if all_servers else 'None'}",
                f"Recent topics: {', '.join(set(recent_topics)) if recent_topics else 'General conversation'}"
            ]

            return "\n".join(context_parts)

        except Exception as e:
            logger.error(f"Failed to generate context: {str(e)}")
            return f"Session {session_id} - context retrieval failed."
    
    def converse_with_session_context(self, session_id: str, user_message: str,
                                    model_id: str = None,
                                    tools_config: Dict = None) -> Dict[str, Any]:
        """Enhanced conversation with full session context using Converse API"""

        model_id = model_id or os.getenv('BEDROCK_MODEL_ID', 'anthropic.claude-3-sonnet-20240229-v1:0')

        # Get conversation history in Converse format
        messages = self.get_bedrock_messages_for_converse(session_id)

        # Add current user message
        messages.append({
            "role": "user",
            "content": [{"text": user_message}]
        })

        # Generate system context
        system_context = self.generate_context_for_system_message(session_id)

        try:
            # Use Converse API with session context
            converse_params = {
                "modelId": model_id,
                "messages": messages,
                "system": [{"text": f"Session Context:\n{system_context}"}],
                "inferenceConfig": {
                    "maxTokens": 4000,
                    "temperature": 0.7
                }
            }

            # Add tools if provided
            if tools_config:
                converse_params["toolConfig"] = tools_config

            response = self.bedrock_runtime.converse(**converse_params)

            # Extract response
            assistant_message = ""
            tool_calls = []

            for content in response['output']['message']['content']:
                if 'text' in content:
                    assistant_message += content['text']
                elif 'toolUse' in content:
                    tool_calls.append(content['toolUse'])

            return {
                'assistant_response': assistant_message,
                'tool_calls': tool_calls,
                'usage': response['usage'],
                'stop_reason': response['stopReason']
            }

        except Exception as e:
            logger.error(f"Converse API call failed: {str(e)}")
            raise

    def end_session(self, session_id: str):
        """End a Bedrock session"""
        try:
            response = self.bedrock_agent_runtime.end_session(sessionId=session_id)
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            logger.info(f"Ended session {session_id}")
            return response
        except Exception as e:
            logger.error(f"Failed to end session: {str(e)}")
            raise

    def delete_session(self, session_id: str):
        """Delete a Bedrock session and all its data"""
        try:
            response = self.bedrock_agent_runtime.delete_session(sessionId=session_id)
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            logger.info(f"Deleted session {session_id}")
            return response
        except Exception as e:
            logger.error(f"Failed to delete session: {str(e)}")
            raise

    def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """Get comprehensive session statistics"""
        try:
            session_info = self.bedrock_agent_runtime.get_session(sessionId=session_id)
            history = self.get_conversation_history(session_id)

            total_tools = sum(turn.get('metadata', {}).get('total_tools', 0) for turn in history)
            successful_tools = sum(turn.get('metadata', {}).get('successful_tools', 0) for turn in history)
            all_servers = set()

            for turn in history:
                servers = turn.get('metadata', {}).get('servers_used', [])
                all_servers.update(servers)

            return {
                'session_id': session_id,
                'session_arn': session_info.get('sessionArn'),
                'created_at': session_info.get('createdTime'),
                'status': session_info.get('status'),
                'total_turns': len(history),
                'total_tools_used': total_tools,
                'successful_tools': successful_tools,
                'failed_tools': total_tools - successful_tools,
                'servers_used': list(all_servers),
                'last_activity': max([turn.get('timestamp') for turn in history]) if history else None
            }

        except Exception as e:
            logger.error(f"Failed to get session stats: {str(e)}")
            return {'error': str(e)}

    def get_or_create_session(self, session_id: str) -> str:
        """Get existing session or create new one - returns session_id"""
        try:
            # Try to get existing session
            session_info = self.bedrock_agent_runtime.get_session(sessionId=session_id)
            logger.info(f"Retrieved existing Bedrock session: {session_id}")
            return session_id
        except:
            # Create new session if it doesn't exist
            session_info = self.create_session(session_id)
            logger.info(f"Created new Bedrock session: {session_id}")
            return session_id

# Global Bedrock session manager instance
session_manager = BedrockSessionManager()
