#!/usr/bin/env python3
"""
Test script to verify MCP Bot optimizations are working correctly
"""

import asyncio
import json
import time
import requests
from typing import Dict, Any

# Test configuration
API_BASE_URL = "http://localhost:8000"

def test_api_connectivity():
    """Test basic API connectivity"""
    print("🔍 Testing API connectivity...")

    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ API is accessible")
            print(f"   - Status: {data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ API returned {response.status_code}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API - is the server running?")
        return False
    except Exception as e:
        print(f"❌ API connectivity test failed: {e}")
        return False

def test_telemetry_endpoint():
    """Test that telemetry endpoint is accessible and returns expected data"""
    print("\n🔍 Testing telemetry endpoint...")

    try:
        response = requests.get(f"{API_BASE_URL}/telemetry", timeout=10)
        if response.status_code == 200:
            data = response.json()

            # Check expected structure
            if "telemetry" in data and "server_status" in data and "optimization_features" in data:
                features = data["optimization_features"]

                print("✅ Telemetry endpoint working correctly")
                print(f"   - Global tool registry: {features.get('global_tool_registry', 0)} tools")
                print(f"   - JSON schema validation: {features.get('json_schema_validation', False)}")

                return True
            else:
                print("❌ Telemetry endpoint missing expected fields")
                print(f"   - Available fields: {list(data.keys())}")
                return False
        else:
            print(f"❌ Telemetry endpoint returned {response.status_code}")
            if response.status_code == 404:
                print("   - Endpoint may not be implemented yet")
            return False

    except Exception as e:
        print(f"❌ Telemetry endpoint test failed: {e}")
        return False

def test_server_status():
    """Test server status and tool registry"""
    print("\n🔍 Testing server status...")

    try:
        response = requests.get(f"{API_BASE_URL}/servers")
        if response.status_code == 200:
            data = response.json()

            # The response is a dict with server names as keys
            connected_servers = [server_info for server_info in data.values() if server_info["status"] == "connected"]
            total_tools = sum(server_info["tools_count"] for server_info in connected_servers)

            print(f"✅ Server status working correctly")
            print(f"   - Connected servers: {len(connected_servers)}")
            print(f"   - Total tools available: {total_tools}")

            return len(connected_servers) > 0
        else:
            print(f"❌ Server status returned {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Server status test failed: {e}")
        return False

def test_chat_with_tools():
    """Test chat functionality with tool usage to verify parallel execution"""
    print("\n🔍 Testing chat with tools (parallel execution)...")
    
    try:
        # Test message that might trigger multiple tools
        test_message = "List my AWS EC2 instances and check the current time"
        
        start_time = time.time()
        
        response = requests.post(f"{API_BASE_URL}/chat", json={
            "message": test_message,
            "use_tools": True,
            "conversation_id": "test_optimization"
        }, timeout=30)
        
        end_time = time.time()
        execution_time = (end_time - start_time) * 1000  # Convert to ms
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Chat with tools working correctly")
            print(f"   - Response time: {execution_time:.2f}ms")

            # Check for tools used in different possible locations
            tools_used = data.get('tools_used', [])
            if not tools_used and 'tool_executions' in data:
                tools_used = data['tool_executions']

            print(f"   - Tools used: {len(tools_used)}")
            print(f"   - Status: {data.get('status', 'unknown')}")

            # Print response snippet for debugging
            response_text = data.get('response', '')
            if len(response_text) > 100:
                response_text = response_text[:100] + "..."
            print(f"   - Response: {response_text}")
            
            return True
        else:
            print(f"❌ Chat endpoint returned {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Chat test failed: {e}")
        return False

def test_performance_metrics():
    """Test that performance metrics are being collected"""
    print("\n🔍 Testing performance metrics collection...")
    
    try:
        # Get initial metrics
        response1 = requests.get(f"{API_BASE_URL}/telemetry")
        initial_metrics = response1.json()["telemetry"]
        
        # Make a chat request
        requests.post(f"{API_BASE_URL}/chat", json={
            "message": "Hello, what's the current time?",
            "use_tools": True,
            "conversation_id": "metrics_test"
        })
        
        # Get updated metrics
        response2 = requests.get(f"{API_BASE_URL}/telemetry")
        updated_metrics = response2.json()["telemetry"]
        
        # Check if metrics increased
        requests_increased = updated_metrics["total_requests"] > initial_metrics["total_requests"]
        
        if requests_increased:
            print("✅ Performance metrics collection working")
            print(f"   - Total requests: {updated_metrics['total_requests']}")
            print(f"   - Total tool calls: {updated_metrics['total_tool_calls']}")
            print(f"   - Success rate: {updated_metrics['success_rate']:.2%}")
            return True
        else:
            print("❌ Performance metrics not updating")
            return False
            
    except Exception as e:
        print(f"❌ Performance metrics test failed: {e}")
        return False

def main():
    """Run all optimization tests"""
    print("🚀 Testing MCP Bot Optimizations")
    print("=" * 50)
    
    tests = [
        test_api_connectivity,
        test_telemetry_endpoint,
        test_server_status,
        test_chat_with_tools,
        test_performance_metrics
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total:.1%})")
    
    if passed == total:
        print("🎉 All optimizations are working correctly!")
    else:
        print("⚠️  Some optimizations may need attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
