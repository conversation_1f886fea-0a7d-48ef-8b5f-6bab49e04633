# Bedrock Session Management Integration Guide

## Overview

This guide shows how to integrate Amazon Bedrock's native session management with your MCP Bot for enhanced context retention and conversation management.

## Key Benefits

### 🚀 **Native AWS Integration**
- **Managed Infrastructure**: No need to manage session storage yourself
- **Automatic Scaling**: Bedrock handles session scaling automatically
- **Built-in Security**: Native AWS security and encryption
- **Cost Optimization**: Pay only for what you use

### 🧠 **Enhanced Context Retention**
- **Persistent Memory**: Sessions persist across application restarts
- **Rich Context**: Store conversation history, tool usage, and metadata
- **Smart Retrieval**: Efficient context retrieval for large conversations
- **Topic Analysis**: Automatic topic detection and categorization

### 🔧 **Simplified Management**
- **Automatic Cleanup**: <PERSON>rock manages session lifecycle
- **No Database Required**: Eliminates need for external storage
- **API-First**: RESTful APIs for all session operations
- **Monitoring**: Built-in session monitoring and analytics

## Architecture Changes

### Before (Custom Session Management)
```
User Request → Custom SessionManager → Local Memory → Bedrock API
                     ↓
              Manual cleanup & persistence
```

### After (Bedrock Session Management)
```
User Request → BedrockSessionManager → Native Bedrock Sessions → Enhanced Context
                     ↓
              Automatic lifecycle management
```

## Integration Steps

### 1. **Replace Session Manager**

The new `BedrockSessionManager` replaces your custom session management:

```python
# OLD: Custom session management
from session_manager import SessionManager, ChatSession
session_manager = SessionManager()

# NEW: Bedrock session management
from session_manager import BedrockSessionManager
session_manager = BedrockSessionManager()
```

### 2. **Update Chat Flow**

Enhanced chat with native Bedrock context:

```python
# Enhanced chat with Bedrock sessions
result = await mcp_manager.chat_with_bedrock_with_context(
    message=user_message,
    session_id=session_id,
    tools_available=available_tools
)
```

### 3. **Session Operations**

```python
# Create session
session_info = session_manager.create_session(session_id, description)

# Store conversation turn
session_manager.store_conversation_turn(
    session_id=session_id,
    invocation_id=invocation_id,
    step_name=step_name,
    user_message=message,
    assistant_response=response,
    tools_used=tools_used
)

# Get conversation history
history = session_manager.get_conversation_history(session_id)

# Get session statistics
stats = session_manager.get_session_stats(session_id)
```

## API Endpoints

### Enhanced Endpoints

All existing endpoints now use Bedrock sessions:

- `POST /chat` - Context-aware chat with Bedrock sessions
- `GET /sessions/{session_id}/history` - Bedrock session history
- `GET /sessions/{session_id}/stats` - Comprehensive session statistics
- `DELETE /sessions/{session_id}` - Delete Bedrock session
- `GET /sessions` - List all active Bedrock sessions

## Configuration

### Environment Variables

```bash
# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key

# Bedrock Model
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-********-v1:0

# Optional: KMS encryption
BEDROCK_KMS_KEY_ARN=arn:aws:kms:region:account:key/key-id
```

### AWS Permissions

Required IAM permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": "arn:aws:bedrock:*::foundation-model/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "bedrock-agent-runtime:CreateSession",
                "bedrock-agent-runtime:GetSession",
                "bedrock-agent-runtime:DeleteSession",
                "bedrock-agent-runtime:EndSession",
                "bedrock-agent-runtime:CreateInvocation",
                "bedrock-agent-runtime:ListInvocations",
                "bedrock-agent-runtime:GetInvocationStep",
                "bedrock-agent-runtime:PutInvocationStep"
            ],
            "Resource": "*"
        }
    ]
}
```

## Migration Guide

### Step 1: Backup Current Sessions
```python
# Export existing sessions before migration
existing_sessions = old_session_manager.get_all_sessions_stats()
```

### Step 2: Update Dependencies
```bash
pip install boto3 --upgrade
```

### Step 3: Test Integration
```python
# Run the example integration
python bedrock_integration_example.py
```

### Step 4: Deploy Enhanced Version
```python
# Use main_enhanced.py instead of main.py
python main_enhanced.py
```

## Performance Benefits

### Memory Usage
- **Before**: Linear growth with conversation history
- **After**: Constant memory usage (offloaded to Bedrock)

### Scalability
- **Before**: Limited by application memory
- **After**: Scales with AWS infrastructure

### Reliability
- **Before**: Sessions lost on application restart
- **After**: Sessions persist across restarts

## Monitoring & Analytics

### Session Metrics
```python
# Get comprehensive session analytics
stats = session_manager.get_session_stats(session_id)
print(f"Total turns: {stats['total_turns']}")
print(f"Tools used: {stats['total_tools_used']}")
print(f"Success rate: {stats['successful_tools']}/{stats['total_tools_used']}")
```

### Cost Tracking
- Monitor Bedrock API usage
- Track session storage costs
- Optimize based on usage patterns

## Best Practices

### 1. **Session ID Strategy**
```python
# Use consistent session ID format
session_id = f"mcp_user_{user_id}_{date}"
```

### 2. **Context Window Management**
- Bedrock automatically manages context windows
- No manual truncation needed
- Optimal token usage

### 3. **Error Handling**
```python
try:
    result = session_manager.converse_with_session_context(...)
except Exception as e:
    logger.error(f"Bedrock session error: {e}")
    # Fallback to stateless conversation
```

### 4. **Session Cleanup**
- Bedrock handles automatic cleanup
- Manual cleanup available if needed
- Monitor session costs

## Troubleshooting

### Common Issues

1. **Session Not Found**
   - Check session ID format
   - Verify AWS permissions
   - Check region configuration

2. **Context Not Retained**
   - Verify session creation
   - Check conversation storage
   - Review error logs

3. **High Costs**
   - Monitor session usage
   - Implement session limits
   - Use appropriate cleanup policies

## Next Steps

1. **Deploy Enhanced Version**: Use `main_enhanced.py`
2. **Monitor Performance**: Track session metrics
3. **Optimize Costs**: Implement usage policies
4. **Scale Up**: Leverage Bedrock's auto-scaling

## Support

For issues or questions:
- Check AWS Bedrock documentation
- Review application logs
- Monitor CloudWatch metrics
- Contact AWS support for Bedrock-specific issues
