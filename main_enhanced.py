"""
Enhanced FastAPI Backend for MCP Client with Context Retention
Extends the existing main.py with session-aware conversation management
"""

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import json
import logging
from contextlib import asynccontextmanager
import uvicorn
import boto3
from botocore.config import Config
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import subprocess
import sys
import os
from dotenv import load_dotenv
import uuid
import weakref
from datetime import datetime, timedelta

# Import our existing main components
from main import (
    MCPServerConfig, ChatMessage, ChatRequest, ChatResponse,
    MCPServerConnection, MCPClientManager, DEFAULT_MCP_SERVERS,
    get_executable_name
)

# Import our new Bedrock session management
from session_manager import session_manager
from enhanced_mcp_manager import EnhancedMCPMixin

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedMCPClientManager(MCPClientManager, EnhancedMCPMixin):
    """
    Enhanced MCP Client Manager with context retention capabilities
    Inherits from existing MCPClientManager and adds session-aware features
    """
    
    def __init__(self):
        super().__init__()
        logger.info("Enhanced MCP Client Manager initialized with context retention")

# Enhanced response model with better tool tracking
class EnhancedChatResponse(BaseModel):
    response: str
    conversation_id: str
    tools_used: List[Dict[str, Any]] = []
    status: str = "success"
    session_stats: Optional[Dict[str, Any]] = None
    context_used: bool = False

# Global enhanced MCP client manager
enhanced_mcp_manager = EnhancedMCPClientManager()

# Background task for session monitoring (Bedrock handles cleanup automatically)
async def session_monitor_worker():
    """Background worker to monitor Bedrock session health"""
    while True:
        try:
            # Log active sessions count
            active_count = len(session_manager.active_sessions)
            if active_count > 0:
                logger.info(f"Active Bedrock sessions: {active_count}")
            await asyncio.sleep(300)  # Monitor every 5 minutes
        except asyncio.CancelledError:
            logger.info("Session monitor worker cancelled")
            break
        except Exception as e:
            logger.error(f"Session monitor error: {e}")
            await asyncio.sleep(60)  # Wait 1 minute on error

# Enhanced lifespan context manager
@asynccontextmanager
async def enhanced_lifespan(app: FastAPI):
    """Enhanced lifespan with session cleanup and MCP server setup"""
    # Startup
    logger.info("Starting Enhanced MCP Client API with Context Retention")
    
    # Start session monitoring task
    monitor_task = asyncio.create_task(session_monitor_worker())
    
    # Auto-configure MCP servers (reuse existing logic)
    if os.getenv("AUTO_CONFIGURE_SERVERS", "true").lower() == "true":
        logger.info("Auto-configuring default MCP servers...")
        
        async def configure_server_safe(server_config):
            """Configure a server with error handling and retry logic"""
            if not server_config.enabled:
                return False, f"Server {server_config.name} is disabled"

            logger.info(f"Adding server: {server_config.name}")
            
            # Retry logic for timeout-prone servers
            max_retries = 2 if server_config.name in ["cost-explorer", "aws-pricing"] else 1
            
            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        logger.info(f"Retry attempt {attempt + 1} for {server_config.name}")
                        await asyncio.sleep(2.0)
                    
                    success = await enhanced_mcp_manager.add_server(server_config)
                    if success:
                        logger.info(f"✅ Successfully configured {server_config.name}")
                        return True, None
                    else:
                        connection = enhanced_mcp_manager.connections.get(server_config.name)
                        error_msg = connection.error if connection else "Unknown error"
                        
                        if "timeout" not in error_msg.lower() or attempt == max_retries - 1:
                            logger.error(f"❌ Failed to configure {server_config.name}: {error_msg}")
                            return False, error_msg
                        else:
                            logger.warning(f"⚠️  Timeout for {server_config.name}, will retry...")
                            
                except Exception as e:
                    if attempt == max_retries - 1:
                        logger.error(f"❌ Error configuring {server_config.name}: {e}")
                        return False, str(e)
                    else:
                        logger.warning(f"⚠️  Exception for {server_config.name}, will retry: {e}")
            
            return False, "Max retries exceeded"
        
        # Configure servers sequentially to reduce resource contention
        results = []
        for config in DEFAULT_MCP_SERVERS:
            logger.info(f"Configuring server: {config.name}")
            result = await configure_server_safe(config)
            results.append(result)
            await asyncio.sleep(1.0)
        
        # Count successful connections
        successful_count = sum(1 for result in results if result[0])
        logger.info(f"🚀 Enhanced startup complete: {successful_count}/{len(DEFAULT_MCP_SERVERS)} servers connected")
        
        if successful_count == 0:
            logger.warning("⚠️  No MCP servers connected, but API will still start")
    
    try:
        yield
    finally:
        # Shutdown
        logger.info("Shutting down Enhanced MCP Client API")
        monitor_task.cancel()
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass
        
        try:
            await enhanced_mcp_manager.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Create Enhanced FastAPI app
app = FastAPI(
    title="Enhanced MCP Client API with Context Retention",
    description="Multi-server MCP client with Bedrock integration and conversation context",
    version="2.0.0",
    lifespan=enhanced_lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Enhanced API Endpoints

@app.get("/")
async def root():
    """Health check endpoint with session stats"""
    session_stats = session_manager.get_all_sessions_stats()
    return {
        "message": "Enhanced MCP Client API with Context Retention is running",
        "status": "healthy",
        "features": ["context_retention", "session_management", "tool_tracking"],
        "session_stats": session_stats
    }

@app.post("/chat", response_model=EnhancedChatResponse)
async def enhanced_chat_endpoint(request: ChatRequest):
    """Enhanced chat endpoint with context retention"""
    try:
        # Get or generate session ID
        conversation_id = request.conversation_id or f"conv_{uuid.uuid4().hex[:8]}"
        
        # Get available tools
        tools_available = list(enhanced_mcp_manager.get_available_tools().keys()) if request.use_tools else []
        
        # Use context-aware chat function
        result = await enhanced_mcp_manager.chat_with_bedrock_with_context(
            message=request.message,
            session_id=conversation_id,
            tools_available=tools_available
        )
        
        # Get session stats from Bedrock
        session_stats = session_manager.get_session_stats(conversation_id)
        
        return EnhancedChatResponse(
            response=result["response"],
            conversation_id=conversation_id,
            tools_used=result["tools_used"],
            status="success" if not result.get("error") else "error",
            session_stats=session_stats,
            context_used=session_stats.get('total_turns', 0) > 0 if session_stats and 'error' not in session_stats else False
        )
        
    except Exception as e:
        logger.error(f"Enhanced chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/sessions/{session_id}/history")
async def get_session_history(session_id: str):
    """Get conversation history for a Bedrock session"""
    try:
        history = session_manager.get_conversation_history(session_id)
        session_stats = session_manager.get_session_stats(session_id)

        if 'error' in session_stats:
            raise HTTPException(status_code=404, detail="Session not found")

        return {
            "session_id": session_id,
            "created_at": session_stats.get('created_at'),
            "last_activity": session_stats.get('last_activity'),
            "message_count": len(history),
            "total_tools_used": session_stats.get('total_tools_used', 0),
            "history": history
        }
    except Exception as e:
        logger.error(f"Error retrieving session history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/sessions/{session_id}/stats")
async def get_session_stats(session_id: str):
    """Get detailed statistics for a Bedrock session"""
    session_stats = session_manager.get_session_stats(session_id)

    if 'error' in session_stats:
        raise HTTPException(status_code=404, detail="Session not found")

    return session_stats

@app.delete("/sessions/{session_id}")
async def clear_session(session_id: str):
    """Delete a Bedrock session"""
    try:
        session_manager.delete_session(session_id)
        return {"message": f"Session {session_id} deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=404, detail="Session not found or could not be deleted")

@app.get("/sessions")
async def list_sessions():
    """List all active Bedrock sessions with basic stats"""
    sessions_info = {}

    for session_id in session_manager.active_sessions.keys():
        try:
            session_stats = session_manager.get_session_stats(session_id)
            if 'error' not in session_stats:
                sessions_info[session_id] = session_stats
        except Exception as e:
            logger.warning(f"Could not get stats for session {session_id}: {e}")

    return {
        "sessions": sessions_info,
        "total_sessions": len(sessions_info),
        "active_sessions_count": len(session_manager.active_sessions)
    }

@app.post("/sessions/cleanup")
async def manual_session_cleanup():
    """Manually trigger session cleanup (Bedrock manages this automatically)"""
    # Bedrock handles session lifecycle automatically
    # This endpoint just reports current status
    return {
        "message": "Bedrock manages session cleanup automatically",
        "active_sessions": len(session_manager.active_sessions),
        "note": "Session cleanup is handled natively by Amazon Bedrock"
    }

# Include existing endpoints from main.py
@app.get("/servers")
async def list_servers():
    """List all configured MCP servers"""
    servers_info = {}
    for name, connection in enhanced_mcp_manager.connections.items():
        servers_info[name] = {
            "name": name,
            "status": connection.status,
            "tools_count": len(connection.tools),
            "resources_count": len(connection.resources),
            "description": connection.config.description,
            "enabled": connection.config.enabled,
            "error": connection.error
        }
    return servers_info

@app.post("/servers")
async def add_server(config: MCPServerConfig):
    """Add a new MCP server"""
    success = await enhanced_mcp_manager.add_server(config)
    if success:
        return {"message": f"Server {config.name} added successfully"}
    else:
        connection = enhanced_mcp_manager.connections.get(config.name)
        error_msg = connection.error if connection else "Unknown error"
        raise HTTPException(status_code=400, detail=f"Failed to add server {config.name}: {error_msg}")

@app.get("/tools")
async def list_tools():
    """List all available tools across all servers"""
    tools = {}
    available_tools = enhanced_mcp_manager.get_available_tools()
    for tool_key, tool_data in available_tools.items():
        tools[tool_key] = {
            "server": tool_data["server"],
            "name": tool_data["tool"]["name"],
            "description": tool_data["tool"]["description"],
            "input_schema": tool_data["tool"].get("input_schema", {})
        }
    return tools

@app.post("/tools/call")
async def call_tool_endpoint(server_name: str, tool_name: str, arguments: Dict[str, Any]):
    """Call a specific tool"""
    result = await enhanced_mcp_manager.call_tool(server_name, tool_name, arguments)
    if result["success"]:
        return result
    else:
        raise HTTPException(status_code=400, detail=result["error"])

if __name__ == "__main__":
    uvicorn.run(
        "main_enhanced:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", "8000")),
        reload=True,
        log_level=os.getenv("LOG_LEVEL", "info")
    )
