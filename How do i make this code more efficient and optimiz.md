<img src="https://r2cdn.perplexity.ai/pplx-full-logo-primary-dark%402x.png" style="height:64px;margin-right:32px"/>

# How do i make this code more efficient and optimized and better tool calling

The code can be made more efficient by tightening MCP session lifecycle management, validating and caching tool schemas, parallelizing tool calls, and completing the Bedrock Converse tool-use loop with structured tool results, streaming, and telemetry, while keeping heavy setup in FastAPI’s lifespan for predictable startup/shutdown behavior. The most impactful changes are adopting AsyncExitStack for MCP connections, using Bedrock’s JSON tool schema to auto-validate inputs, and running multiple model-requested tools concurrently with asyncio.gather to reduce end-to-end latency.[^1][^2][^3][^4]

## Biggest wins

- Use AsyncExitStack to manage stdio_client and ClientSession so connect/cleanup is correct, exception-safe, and simpler than manual __aenter__/__aexit__ calls.[^3]
- Validate tool inputs against the JSON schema provided in toolSpec.inputSchema.json and return structured toolResult with json and status to the model for error-aware reasoning.[^1]
- If the assistant requests multiple toolUse blocks in one turn, execute the corresponding tools in parallel and return all toolResult blocks in one user message to minimize round trips.[^2][^1]


## MCP lifecycle

Wrap stdio_client and ClientSession in an AsyncExitStack and let the stack own enter/exit of both; this removes the need for ad‑hoc safe cleanup and greatly reduces resource leak risk. List tools once after initialize and keep a lightweight registry keyed by tool.name, since the quickstart pattern shows listing tools immediately after session initialization. This approach mirrors the reference client pattern and simplifies your reconnect logic because you centralize all acquisition/release in one context boundary.[^3]

Example (structure only):

```python
from contextlib import AsyncExitStack

class MCPServerConnection:
    def __init__(self, config):
        self.config = config
        self.exit_stack = AsyncExitStack()
        self.session = None
        self.tools = {}
        self.status = "disconnected"

    async def connect(self):
        try:
            transport = await self.exit_stack.enter_async_context(
                stdio_client(StdioServerParameters(
                    command=self.config.command, args=self.config.args, env=self.config.env
                ))
            )
            read, write = transport
            self.session = await self.exit_stack.enter_async_context(ClientSession(read, write))
            await self.session.initialize()
            tools_result = await self.session.list_tools()
            self.tools = {t.name: {"description": t.description, "input_schema": getattr(t, "inputSchema", {})}
                          for t in tools_result.tools}
            self.status = "connected"
            return True
        except Exception:
            await self.exit_stack.aclose()
            self.status = "error"
            return False

    async def disconnect(self):
        await self.exit_stack.aclose()
        self.status = "disconnected"
```

This matches the MCP client quickstart’s pattern of entering both stdio_client and ClientSession via an async context for robust lifecycle management.[^3]

## Tool registry and validation

Maintain a dictionary mapping tool name → {server, json_schema} at startup and refresh it only on reconnect; this avoids scanning arrays in hot paths and follows the recommended flow of listing tools right after initialize. Use the JSON schema in toolSpec.inputSchema.json to validate arguments before calling the server tool, leveraging “required” and “properties” the same way Bedrock’s tool-use specification describes schemas. During testing, allow forcing specific tools with toolChoice to verify end-to-end correctness; otherwise keep toolChoice in auto mode as already done.[^1][^3]

Example (validation sketch):

```python
from jsonschema import validate, Draft202012Validator, ValidationError

def validate_args(schema_json, args):
    Draft202012Validator.check_schema(schema_json)
    validate(instance=args, schema=schema_json)
```

This aligns with Bedrock’s tool definition being a JSON schema that declares required fields and types for inputs.[^1]

## Faster tool calling

If the model returns multiple toolUse blocks in one assistant message, dispatch them concurrently with asyncio.gather and then send a single user message containing all toolResult blocks keyed by their toolUseId. This preserves the required structure (toolUse → toolResult with same toolUseId) while collapsing N serial round trips into one batched response, improving latency without changing semantics.[^1]

Example:

```python
# Collect toolUse blocks
requests = []
for c in assistant_message["content"]:
    tu = c.get("toolUse")
    if tu:
        requests.append((tu["toolUseId"], tu["name"], tu.get("input", {})))

# Run all tools concurrently
results = await asyncio.gather(*[
    self.call_tool(server_for[name], name, validate_and_fix(schema_for[name], inp))
    for _, name, inp in requests
], return_exceptions=True)

# Build one toolResult message with json content
tool_result_msg = {"role": "user", "content": []}
for (toolUseId, _, _), result in zip(requests, results):
    ok, payload = format_for_model(result)
    tool_result_msg["content"].append({
        "toolResult": {
            "toolUseId": toolUseId,
            "content": [{"json": payload}] if ok else [{"text": payload}],
            "status": "success" if ok else "error"
        }
    })
messages.append(tool_result_msg)
```

This matches Bedrock’s prescribed flow of receiving toolUse, executing tools, and replying with toolResult objects before asking the model to continue.[^1]

## Bedrock loop correctness

The loop should continue until stopReason is end_turn (or a terminal reason), and it’s valid to see stop_reason values including tool_use, end_turn, stop_sequence, and max_tokens as indicated by the Converse API docs. Capture and persist usage and metrics from the Converse response for observability and cost tracking since the API returns token usage and latency in usage and metrics fields. When sending tool results, prefer the json content block and set status to error for failures so the model can recover with error-aware reasoning, which the tool use guide shows explicitly.[^2][^1]

Example (finalization):

```python
resp = bedrock.converse(**params)
stop = resp.get("stopReason")
if stop == "tool_use":
    ...
else:
    usage = resp.get("usage", {})
    metrics = resp.get("metrics", {})
    persist(usage, metrics)  # inputTokens, outputTokens, latencyMs
```

This directly follows the documented fields and termination reasons of the Converse response.[^2]

## Stream for lower latency

Consider switching to ConverseStream to emit contentBlockDelta chunks while the model is thinking; this reduces time-to-first-token and still supports tool use events via partial toolUse deltas. The stream enumerates messageStart/contentBlockStart/contentBlockDelta/contentBlockStop/messageStop in order, exposing usage and metrics at the end to match the non-streaming telemetry.[^2]

## FastAPI app structure

Keep heavy one-time initialization inside the lifespan async context so startup happens once before serving requests and cleanup runs once on shutdown, which is the recommended approach in FastAPI. Use the lifespan block to connect MCP servers and warm caches, and ensure shutdown awaits a unified cleanup path that closes all resources exactly once for predictable teardown. This pattern is explicitly documented to share long-lived resources safely across requests without per-request overhead.[^4]

## Telemetry and controls

Augment converse calls with system prompts clarifying tool behavior, and optionally request additional model response fields when needed (e.g., stop sequences) using additionalModelResponseFieldPaths, which the Converse API supports. Record token usage and latency for each turn to a store to inform rate-limits and backoff policy alongside the boto3 client’s adaptive retries already configured. This yields measurable service SLOs for tool latency and model iteration counts grounded in the API’s usage/metrics contracts.[^5][^2]

## Minimal patches to apply

- Replace manual session enter/exit with AsyncExitStack for stdio_client and ClientSession to ensure exception-safe cleanup across all code paths.[^3]
- Cache a tool registry at connect time and validate inputs with the tool’s JSON schema; send toolResult as json and “status”: “error” on failures.[^1]
- Run all toolUse requests from one assistant turn concurrently with asyncio.gather and then append one user toolResult message containing all results, then continue until end_turn.[^2][^1]

These changes align the implementation with the official patterns for MCP client resource management and Bedrock’s Converse tool-use protocol, improving correctness, latency, and maintainability without changing external APIs.[^3][^2][^1]
<span style="display:none">[^10][^11][^12][^13][^14][^15][^16][^17][^18][^19][^20][^21][^6][^7][^8][^9]</span>

<div style="text-align: center">⁂</div>

[^1]: https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use-inference-call.html

[^2]: https://docs.aws.amazon.com/bedrock/latest/userguide/conversation-inference-call.html

[^3]: https://modelcontextprotocol.io/quickstart/client

[^4]: https://fastapi.tiangolo.com/advanced/events/

[^5]: https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/bedrock-runtime/client/converse.html

[^6]: paste.txt

[^7]: https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html

[^8]: https://docs.aws.amazon.com/bedrock/latest/userguide/tool-use-examples.html

[^9]: https://docs.aws.amazon.com/bedrock/latest/userguide/bedrock-runtime_example_bedrock-runtime_Scenario_ToolUseDemo_CohereCommand_section.html

[^10]: https://modelcontextprotocol.info/docs/quickstart/client/

[^11]: https://www.byteplus.com/en/topic/518004

[^12]: https://python.langchain.com/api_reference/aws/chat_models/langchain_aws.chat_models.bedrock_converse.ChatBedrockConverse.html

[^13]: https://www.cloudnativedeepdive.com/model-context-protocol-mcp-servers-for-the-absolute-beginner/

[^14]: https://www.getorchestra.io/guides/tutorial-understanding-and-using-fastapi-lifespan-events

[^15]: https://docs.aws.amazon.com/code-library/latest/ug/bedrock-runtime_example_bedrock-runtime_Scenario_ToolUseDemo_CohereCommand_section.html

[^16]: https://github.com/modelcontextprotocol/python-sdk

[^17]: https://stackoverflow.com/questions/79570987/how-to-add-a-shutdown-event-in-fastapi-using-lifespan-in-middleware

[^18]: https://dev.to/aws-builders/a-step-by-step-guide-on-how-to-use-the-amazon-bedrock-converse-api-2mnl

[^19]: https://www.philschmid.de/mcp-introduction

[^20]: https://fastapi.tiangolo.com/advanced/testing-events/

[^21]: https://modelcontextprotocol.io/docs/concepts/architecture

