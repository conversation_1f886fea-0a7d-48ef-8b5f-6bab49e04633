#!/usr/bin/env python3
"""
Direct Cost Analysis Test Script using main2.py (NO OPTIMIZER)
Tests the MCP cost analysis tools directly
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Import from main2.py (the fixed version without optimizer)
from main2 import MCPClientManager, MCPServerConfig

load_dotenv()

def get_executable_name(name: str) -> str:
    """Get the executable name for the MCP server"""
    return name

# Define servers
DEFAULT_MCP_SERVERS = [
    MCPServerConfig(
        name="cost-explorer",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-cost-explorer-mcp-server@latest",
            get_executable_name("awslabs.cost-explorer-mcp-server")
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        description="AWS Cost Explorer MCP Server for cost analysis and billing insights",
        enabled=True
    )
]

async def test_cost_analysis():
    """Test cost analysis functionality using main2.py"""
    
    print("🔍 Testing AWS Cost Analysis Tools (MAIN2 - NO OPTIMIZER)")
    print("=" * 60)
    
    # Initialize MCP client manager from main2.py
    manager = MCPClientManager()
    
    try:
        # Add cost-explorer server
        print("📡 Adding cost-explorer server...")
        success = await manager.add_server(DEFAULT_MCP_SERVERS[0])
        if success:
            print("✅ cost-explorer connected successfully")
        else:
            print("❌ Failed to connect to cost-explorer")
            return
        
        # Get available tools
        tools = manager.get_available_tools()
        print(f"📋 Found {len(tools)} available tools")
        
        # List cost-related tools
        cost_tools = [name for name in tools.keys() if 'cost' in name.lower()]
        print(f"💰 Cost-related tools: {cost_tools}")
        
        # Calculate date range (last 10 days)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=10)
        
        # Test 1: Get Bedrock costs for last 10 days
        print(f"\n📊 Test 1: Bedrock costs from {start_date} to {end_date}")
        print("-" * 50)
        
        bedrock_params = {
            "date_range": {
                "start": start_date.strftime("%Y-%m-%d"),
                "end": end_date.strftime("%Y-%m-%d")
            },
            "granularity": "DAILY",
            "group_by": "SERVICE",
            "filter_expression": {
                "dimensions": {
                    "key": "SERVICE",
                    "values": ["Amazon Bedrock"],
                    "match_options": ["EQUALS"]
                }
            }
        }
        
        print("📞 Calling get_cost_and_usage for Bedrock...")
        print(f"Parameters: {json.dumps(bedrock_params, indent=2)}")
        
        result = await manager.call_tool("cost-explorer", "get_cost_and_usage", bedrock_params)
        
        if result["success"]:
            print("✅ Bedrock cost analysis successful!")
            print("📈 Results:")
            print(json.dumps(result["result"], indent=2))
        else:
            print(f"❌ Bedrock cost analysis failed: {result.get('error', 'Unknown error')}")
        
        # Test 2: Get overall account costs for ap-south-1
        print(f"\n🌏 Test 2: Account costs in ap-south-1 from {start_date} to {end_date}")
        print("-" * 50)
        
        region_params = {
            "date_range": {
                "start": start_date.strftime("%Y-%m-%d"),
                "end": end_date.strftime("%Y-%m-%d")
            },
            "granularity": "DAILY",
            "group_by": "SERVICE"
        }
        
        print("📞 Calling get_cost_and_usage for ap-south-1...")
        print(f"Parameters: {json.dumps(region_params, indent=2)}")
        
        result = await manager.call_tool("cost-explorer", "get_cost_and_usage", region_params)
        
        if result["success"]:
            print("✅ Regional cost analysis successful!")
            print("📊 Results:")
            print(json.dumps(result["result"], indent=2))
        else:
            print(f"❌ Regional cost analysis failed: {result.get('error', 'Unknown error')}")
        
        # Test 3: Get cost forecast
        print(f"\n🔮 Test 3: 30-day cost forecast")
        print("-" * 50)
        
        forecast_params = {
            "date_range": {
                "start": end_date.strftime("%Y-%m-%d"),
                "end": (end_date + timedelta(days=30)).strftime("%Y-%m-%d")
            },
            "granularity": "MONTHLY",
            "metric": "BLENDED_COST"
        }
        
        print("📞 Calling get_cost_forecast...")
        print(f"Parameters: {json.dumps(forecast_params, indent=2)}")
        
        result = await manager.call_tool("cost-explorer", "get_cost_forecast", forecast_params)
        
        if result["success"]:
            print("✅ Cost forecast successful!")
            print("🔮 Results:")
            print(json.dumps(result["result"], indent=2))
        else:
            print(f"❌ Cost forecast failed: {result.get('error', 'Unknown error')}")
        
    except Exception as e:
        print(f"❌ Error during cost analysis: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        await manager.cleanup()
        print("✅ Cleanup complete")

if __name__ == "__main__":
    asyncio.run(test_cost_analysis())
