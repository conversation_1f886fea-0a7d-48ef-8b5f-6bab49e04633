"""
Enhanced MCP Manager with Bedrock Session Management
Integrates MCP functionality with native Bedrock session management
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from session_manager import session_manager

logger = logging.getLogger(__name__)

class EnhancedMCPMixin:
    """
    Mixin class to add Bedrock session-aware functionality to existing MCPClientManager
    Uses native Bedrock session management for context retention
    """

    async def chat_with_bedrock_with_context(self, message: str, session_id: str,
                                           tools_available: List[str] = None) -> Dict[str, Any]:
        """
        Enhanced Bedrock chat with native session context retention using new session manager

        Args:
            message: User's message
            session_id: Session identifier for context retention
            tools_available: List of available tool keys

        Returns:
            Dict containing response, tools_used, and session_id
        """
        try:
            # Get or create session using new session manager
            chat_session = session_manager.get_or_create_session(session_id)

            # Get conversation history for context
            historical_messages = chat_session.get_bedrock_messages(max_turns=8)

            # Build current messages with history
            current_messages = historical_messages + [{
                "role": "user",
                "content": [{"text": message}]
            }]

            # Enhanced system message with session context
            system_message = self._build_context_aware_system_message(chat_session, tools_available)

            # Prepare tool configuration
            tool_config = self._build_tool_config_for_bedrock(tools_available)

            # Execute conversation with context using existing Bedrock client
            result = await self._execute_contextual_conversation(
                current_messages, system_message, tool_config, session_id
            )

            # Store conversation turn in session using new session manager
            chat_session.add_turn(
                user_message=message,
                assistant_response=result["response"],
                tools_used=result["tools_used"]
            )

            logger.info(f"Completed contextual chat for session {session_id}: "
                       f"{len(result['tools_used'])} tools used")

            return result

        except Exception as e:
            logger.error(f"Error in contextual chat for session {session_id}: {e}")
            return {
                "response": f"I apologize, but I encountered an error: {str(e)}",
                "tools_used": [],
                "session_id": session_id,
                "error": True
            }

    def _build_context_aware_system_message(self, chat_session, tools_available: List[str] = None) -> str:
        """Build enhanced system message with session context"""
        base_message = """You are an AI assistant with access to various tools through MCP (Model Context Protocol) servers.
You can help with AWS cost analysis, CloudFormation management, pricing analysis, and more.

When using tools, be thorough and provide detailed explanations of the results."""

        # Add session context
        session_context = chat_session.get_context_for_bedrock()
        if session_context:
            base_message += f"\n\nSession Context:\n{session_context}"

        # Add available tools context
        if tools_available:
            base_message += f"\n\nAvailable tools: {', '.join(tools_available[:10])}"
            if len(tools_available) > 10:
                base_message += f" and {len(tools_available) - 10} more tools."

        return base_message

    async def _execute_contextual_conversation(self, messages: List[Dict], system_message: str,
                                             tool_config: Dict, session_id: str) -> Dict[str, Any]:
        """Execute conversation with full context using existing Bedrock infrastructure"""
        try:
            # Use the existing Bedrock conversation method from the base class
            # This assumes the base class has a method to handle Bedrock conversations
            response = await self.converse_with_bedrock(
                messages=messages,
                system_message=system_message,
                tool_config=tool_config
            )

            # Execute any tool calls
            tools_used = []
            if response.get('tool_calls'):
                tools_used = await self._execute_tool_calls(response['tool_calls'], session_id)

            return {
                "response": response.get('assistant_response', response.get('content', '')),
                "tools_used": tools_used,
                "session_id": session_id,
                "usage": response.get('usage', {}),
                "stop_reason": response.get('stop_reason', 'end_turn')
            }

        except Exception as e:
            logger.error(f"Error in contextual conversation: {e}")
            raise
    
    def _build_tool_config_for_bedrock(self, tools_available: List[str] = None) -> Optional[Dict]:
        """Build tool configuration for Bedrock Converse API"""
        if not tools_available:
            return None

        available_tools = self.get_available_tools()
        tools = []

        for tool_key in tools_available:
            if tool_key in available_tools:
                tool_data = available_tools[tool_key]
                tool = tool_data["tool"]

                # Ensure proper schema format for Bedrock
                input_schema = tool.get("input_schema", {})
                if not input_schema:
                    input_schema = {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }

                # Ensure required structure
                if "type" not in input_schema:
                    input_schema["type"] = "object"
                if "properties" not in input_schema:
                    input_schema["properties"] = {}

                tools.append({
                    "toolSpec": {
                        "name": tool["name"],
                        "description": tool["description"] or f"Tool from server {tool_data['server']}",
                        "inputSchema": {
                            "json": input_schema
                        }
                    }
                })

        if tools:
            return {
                "tools": tools,
                "toolChoice": {"auto": {}}
            }

        return None
    
    async def _execute_tool_calls(self, tool_calls: List[Dict], session_id: str) -> List[Dict]:
        """Execute tool calls from Bedrock and return results"""
        tools_used = []

        for tool_call in tool_calls:
            tool_name = tool_call.get('name')
            tool_input = tool_call.get('input', {})

            logger.info(f"Executing tool: {tool_name} with input: {tool_input}")

            # Find server for this tool
            server_name = self._find_server_for_tool(tool_name)

            if server_name:
                try:
                    result = await self.call_tool(server_name, tool_name, tool_input)

                    # Track tool usage with context
                    tool_usage = {
                        "tool_name": tool_name,
                        "server_name": server_name,
                        "input": tool_input,
                        "success": result["success"],
                        "session_id": session_id
                    }

                    # Store truncated result for context (avoid memory bloat)
                    if result["success"]:
                        result_str = str(result.get("result", ""))
                        tool_usage["result"] = result_str[:500] + "..." if len(result_str) > 500 else result_str
                    else:
                        tool_usage["error"] = result.get("error", "Unknown error")

                    tools_used.append(tool_usage)

                except Exception as e:
                    logger.error(f"Tool execution error for {tool_name}: {e}")
                    tools_used.append({
                        "tool_name": tool_name,
                        "server_name": server_name,
                        "input": tool_input,
                        "success": False,
                        "error": str(e),
                        "session_id": session_id
                    })
            else:
                logger.error(f"Server not found for tool: {tool_name}")
                tools_used.append({
                    "tool_name": tool_name,
                    "server_name": None,
                    "input": tool_input,
                    "success": False,
                    "error": f"Tool {tool_name} not found",
                    "session_id": session_id
                })

        return tools_used
    
    def _find_server_for_tool(self, tool_name: str) -> Optional[str]:
        """Find which server provides a specific tool"""
        available_tools = self.get_available_tools()

        for tool_data in available_tools.values():
            if tool_data["tool"]["name"] == tool_name:
                return tool_data["server"]

        return None
    

