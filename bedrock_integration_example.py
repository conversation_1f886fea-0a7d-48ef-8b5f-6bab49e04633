"""
Example integration showing how to use the new Bedrock Session Management
with your MCP Bot for enhanced context retention
"""

import asyncio
import uuid
from datetime import datetime
from session_manager import session_manager
from main_enhanced import EnhancedMCPClientManager

class BedrockMCPBot:
    """
    Example MCP Bot using Bedrock Session Management
    Demonstrates the integration benefits and usage patterns
    """
    
    def __init__(self):
        self.mcp_manager = EnhancedMCPClientManager()
        self.session_manager = session_manager
        
    async def handle_user_message(self, user_id: str, message: str) -> dict:
        """
        Handle user message with full Bedrock session context
        
        Benefits:
        - Native AWS session management
        - Automatic context retention
        - Built-in session lifecycle management
        - Enhanced conversation continuity
        """
        
        # Generate session ID for user
        session_id = f"mcp_user_{user_id}"
        
        try:
            # Use the enhanced chat with Bedrock context
            available_tools = list(self.mcp_manager.get_available_tools().keys())
            
            result = await self.mcp_manager.chat_with_bedrock_with_context(
                message=message,
                session_id=session_id,
                tools_available=available_tools
            )
            
            return {
                'success': True,
                'response': result['response'],
                'tools_used': result['tools_used'],
                'session_id': session_id,
                'context_retained': True
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'session_id': session_id
            }
    
    async def get_session_summary(self, user_id: str) -> dict:
        """Get comprehensive session summary"""
        session_id = f"mcp_user_{user_id}"
        
        try:
            stats = self.session_manager.get_session_stats(session_id)
            history = self.session_manager.get_conversation_history(session_id)
            
            return {
                'session_stats': stats,
                'conversation_count': len(history),
                'recent_topics': self._analyze_topics(history),
                'tool_usage_summary': self._analyze_tool_usage(history)
            }
        except Exception as e:
            return {'error': str(e)}
    
    def _analyze_topics(self, history: list) -> list:
        """Analyze conversation topics from history"""
        topics = set()
        for turn in history[-5:]:  # Last 5 turns
            user_msg = turn.get('user_message', '').lower()
            if 'cost' in user_msg or 'billing' in user_msg:
                topics.add('cost_analysis')
            if 'cloudformation' in user_msg or 'stack' in user_msg:
                topics.add('infrastructure')
            if 'pricing' in user_msg:
                topics.add('pricing_analysis')
        return list(topics)
    
    def _analyze_tool_usage(self, history: list) -> dict:
        """Analyze tool usage patterns"""
        servers_used = set()
        total_tools = 0
        successful_tools = 0
        
        for turn in history:
            metadata = turn.get('metadata', {})
            total_tools += metadata.get('total_tools', 0)
            successful_tools += metadata.get('successful_tools', 0)
            servers_used.update(metadata.get('servers_used', []))
        
        return {
            'total_tools_executed': total_tools,
            'successful_tools': successful_tools,
            'success_rate': successful_tools / max(1, total_tools),
            'servers_used': list(servers_used)
        }

# Example usage
async def demo_bedrock_integration():
    """Demonstrate the Bedrock integration benefits"""
    
    bot = BedrockMCPBot()
    user_id = "demo_user_123"
    
    print("🚀 Bedrock Session Management Demo")
    print("=" * 50)
    
    # Simulate conversation with context retention
    messages = [
        "What are my AWS costs for this month?",
        "Can you break that down by service?",
        "What about last month's costs?",
        "How do these compare to my budget?"
    ]
    
    for i, message in enumerate(messages, 1):
        print(f"\n💬 Turn {i}: {message}")
        
        result = await bot.handle_user_message(user_id, message)
        
        if result['success']:
            print(f"🤖 Response: {result['response'][:100]}...")
            print(f"🔧 Tools used: {len(result['tools_used'])}")
            print(f"📝 Context retained: {result['context_retained']}")
        else:
            print(f"❌ Error: {result['error']}")
    
    # Get session summary
    print(f"\n📊 Session Summary:")
    summary = await bot.get_session_summary(user_id)
    if 'error' not in summary:
        print(f"   Conversations: {summary['conversation_count']}")
        print(f"   Topics: {summary['recent_topics']}")
        print(f"   Tool success rate: {summary['tool_usage_summary']['success_rate']:.2%}")
    
    print("\n✅ Demo completed!")

if __name__ == "__main__":
    # Run the demo
    asyncio.run(demo_bedrock_integration())
