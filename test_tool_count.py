#!/usr/bin/env python3
"""
Simple test to verify tool count tracking is working
"""

import requests
import json
import time

API_BASE_URL = "http://localhost:8000"

def test_tool_count_tracking():
    """Test that tool count is properly tracked in telemetry"""
    print("🔍 Testing tool count tracking...")
    
    try:
        # First, get initial telemetry
        print("📊 Getting initial telemetry...")
        response = requests.get(f"{API_BASE_URL}/telemetry", timeout=10)
        if response.status_code == 200:
            initial_data = response.json()
            initial_tool_calls = initial_data.get("telemetry", {}).get("total_tool_calls", 0)
            print(f"   - Initial tool calls: {initial_tool_calls}")
        else:
            print(f"❌ Failed to get initial telemetry: {response.status_code}")
            return False
        
        # Send a chat request that should trigger tools
        print("\n💬 Sending chat request to trigger tools...")
        chat_request = {
            "message": "What's today's date and check AWS EC2 costs for January 2025?",
            "use_tools": True,
            "conversation_id": "test_tool_count"
        }
        
        start_time = time.time()
        response = requests.post(f"{API_BASE_URL}/chat", json=chat_request, timeout=45)
        end_time = time.time()
        
        if response.status_code == 200:
            chat_data = response.json()
            print(f"✅ Chat request completed in {(end_time - start_time):.2f}s")
            print(f"   - Response length: {len(chat_data.get('response', ''))}")
            print(f"   - Tools used (from response): {len(chat_data.get('tools_used', []))}")
            print(f"   - Status: {chat_data.get('status', 'unknown')}")
            
            # Get updated telemetry
            print("\n📊 Getting updated telemetry...")
            response = requests.get(f"{API_BASE_URL}/telemetry", timeout=10)
            if response.status_code == 200:
                final_data = response.json()
                final_tool_calls = final_data.get("telemetry", {}).get("total_tool_calls", 0)
                tool_calls_diff = final_tool_calls - initial_tool_calls
                
                print(f"   - Final tool calls: {final_tool_calls}")
                print(f"   - Tool calls added: {tool_calls_diff}")
                
                if tool_calls_diff > 0:
                    print(f"✅ Tool count tracking is working! {tool_calls_diff} tools were tracked.")
                    return True
                else:
                    print("❌ No tools were tracked in telemetry")
                    return False
            else:
                print(f"❌ Failed to get final telemetry: {response.status_code}")
                return False
        else:
            print(f"❌ Chat request failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   - Error: {error_data}")
            except:
                print(f"   - Raw response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API - is the server running?")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_api_status():
    """Quick test to check if API is running"""
    print("🔍 Testing API status...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API is running - {data.get('message', 'Unknown status')}")
            return True
        else:
            print(f"❌ API returned {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ API is not accessible - server may not be running")
        return False
    except Exception as e:
        print(f"❌ API status check failed: {e}")
        return False

def main():
    print("🚀 Testing Tool Count Tracking")
    print("=" * 50)
    
    # Check if API is running
    if not test_api_status():
        print("\n❌ Cannot proceed - API is not accessible")
        print("Please start the server with: python main.py")
        return False
    
    # Test tool count tracking
    success = test_tool_count_tracking()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Tool count tracking is working correctly!")
    else:
        print("⚠️  Tool count tracking needs attention")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
